<launch>
  <!-- This launch file runs the bringup without RVIZ on Device 1 (robot) -->
  
  <arg name="model" default="$(find robot_description)/urdf/Robot.xacro"/>
  <arg name="gui" default="false"/>

  <!-- RVIZ is intentionally not included as it will run on Device 2 -->
  
  <param name="robot_description" command="$(find xacro)/xacro $(arg model)"/>
  <param name="use_gui" value="$(arg gui)"/>
  
  <!-- For motor control and encoder arduino -->
  <node name="rosserial1" pkg="rosserial_python" type="serial_node.py" args="/dev/ttyUSB_arduino">
    <param name="baud" value="57600" />
  </node>
  
  <include file="$(find weednix_sensors)/launch/imu_test.launch" />
  <node pkg="weednix_sensors" type="enc_odom.py" name="encoder_odom" output="screen" />
  
  <node pkg="robot_localization" type="ekf_localization_node" name="ekf_filter_node" output="screen">
        <rosparam command="load" file="$(find weednix_launch)/config/ekf.yaml" />
  </node>

  <include file="$(find weednix_launch)/launch/gps_real.launch" />

  <!-- Make sure robot_state_publisher is running with correct parameters -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher">
    <param name="publish_frequency" value="50.0"/>
    <param name="ignore_timestamp" value="true"/>
  </node>

  <!-- Add joint_state_publisher to publish wheel joint states -->
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
    <param name="use_gui" value="false"/>
    <param name="rate" value="50"/>
  </node>

  <!-- Add static transform for any missing links if needed -->
  <node pkg="tf" type="static_transform_publisher" name="base_to_wheels_broadcaster" 
        args="0 0 0 0 0 0 base_link wheel_base_link 100" />
</launch>
