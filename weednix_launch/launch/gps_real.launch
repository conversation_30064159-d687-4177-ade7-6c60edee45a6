<launch>

  <node pkg="nmea_navsat_driver" type="nmea_serial_driver" name="nmea_serial_driver" output="screen">
        <param name="port" value="/dev/ttyUSB_gps"/>
        <param name="baud" value="38400"/>
  </node>
    
  <node name="geofencing_node" pkg="weednix_sensors" type="geofencing_node.py"  output="screen">
	  <param name="geojson_file" value="$(find weednix_sensors)/config/boundaries.geojson" />
	  <param name="gps_error_radius" value="2.5" />  <!-- GPS error radius in meters -->
	  <param name="gps_topic" value="/fix" />
	  <param name="update_rate" value="50.0" />  <!-- Check frequency in Hz -->
  </node>



</launch>
