import React, { useState, useContext, useEffect, useRef } from 'react';
import { RosContext } from './RosConnection';
import ROSLIB from 'roslib';
import './CameraFeed.css';

const CameraFeed = () => {
  const { ros, isConnected } = useContext(RosContext);
  const [imageData] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState(null);
  const [imageTopic, setImageTopic] = useState('/camera_sim/color/image_raw');
  const canvasRef = useRef(null);
  const imageSubscriberRef = useRef(null);

  // Available camera topics
  const cameraTopics = [
    '/camera_sim/color/image_raw',
    '/camera/rgb/image_rect_color',
    '/camera/color/image_raw',
    '/kinect2/qhd/image_color_rect'
  ];

  useEffect(() => {
    const initializeStream = () => {
      if (!isConnected || !ros) {
        setIsStreaming(false);
        setError('Not connected to ROS');
        return;
      }

      startImageStream();
    };

    initializeStream();

    return () => {
      stopImageStream();
    };
  }, [ros, isConnected, imageTopic]); // eslint-disable-line react-hooks/exhaustive-deps

  const startImageStream = () => {
    if (!ros || !isConnected) return;

    try {
      // Clean up existing subscription
      stopImageStream();

      // Try to use web_video_server first (HTTP streaming)
      // const webVideoUrl = `http://localhost:8080/stream?topic=${imageTopic}&type=mjpeg`;

      // Test if web_video_server is available
      fetch('http://localhost:8080/stream_viewer?topic=' + encodeURIComponent(imageTopic))
        .then(response => {
          if (response.ok) {
            // web_video_server is available, use HTTP streaming
            setError(null);
            setIsStreaming(true);
            console.log(`Using web_video_server for topic: ${imageTopic}`);
          } else {
            throw new Error('web_video_server not available');
          }
        })
        .catch(() => {
          // Fallback: Try direct ROS topic subscription for compressed images
          tryDirectImageSubscription();
        });

    } catch (err) {
      console.error('Error starting image stream:', err);
      setError('Failed to start camera stream');
      setIsStreaming(false);
    }
  };

  const tryDirectImageSubscription = () => {
    try {
      // Try compressed image topic first
      const compressedTopic = imageTopic.replace('/image_raw', '/image_raw/compressed');

      imageSubscriberRef.current = new ROSLIB.Topic({
        ros: ros,
        name: compressedTopic,
        messageType: 'sensor_msgs/CompressedImage'
      });

      imageSubscriberRef.current.subscribe((message) => {
        try {
          // Create image from compressed data
          const canvas = canvasRef.current;
          if (!canvas) return;

          const ctx = canvas.getContext('2d');
          const img = new Image();

          // Convert ROS compressed image to data URL
          const imageData = 'data:image/jpeg;base64,' + message.data;

          img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            setIsStreaming(true);
            setError(null);
          };

          img.onerror = () => {
            setError('Failed to decode image data');
            setIsStreaming(false);
          };

          img.src = imageData;

        } catch (err) {
          console.error('Error processing compressed image:', err);
          setError('Error processing image data');
          setIsStreaming(false);
        }
      });

      console.log(`Subscribed to compressed image topic: ${compressedTopic}`);
      setError(null);

    } catch (err) {
      console.error('Error subscribing to compressed image:', err);
      setError('Camera streaming requires web_video_server or compressed image topics. Install with: sudo apt-get install ros-noetic-web-video-server');
      setIsStreaming(false);
    }
  };

  const stopImageStream = () => {
    if (imageSubscriberRef.current) {
      imageSubscriberRef.current.unsubscribe();
      imageSubscriberRef.current = null;
    }
    setIsStreaming(false);
  };

  const handleTopicChange = (newTopic) => {
    setImageTopic(newTopic);
  };

  const getStatusColor = () => {
    if (!isConnected) return '#f44336';
    if (isStreaming) return '#4CAF50';
    return '#ff9800';
  };

  const getStatusText = () => {
    if (!isConnected) return 'Disconnected';
    if (isStreaming) return 'Streaming';
    if (error) return 'Error';
    return 'Connecting...';
  };

  return (
    <div className="camera-feed">
      <div className="camera-header">
        <h2>Camera Feed</h2>
        <div className="camera-controls">
          <select 
            value={imageTopic} 
            onChange={(e) => handleTopicChange(e.target.value)}
            className="topic-selector"
            disabled={!isConnected}
          >
            {cameraTopics.map(topic => (
              <option key={topic} value={topic}>{topic}</option>
            ))}
          </select>
          <div className="status-indicator">
            <div 
              className="status-dot" 
              style={{ backgroundColor: getStatusColor() }}
            ></div>
            <span className="status-text">{getStatusText()}</span>
          </div>
        </div>
      </div>

      <div className="camera-container">
        {error && (
          <div className="error-message">
            <p>⚠️ {error}</p>
            <button onClick={startImageStream} disabled={!isConnected}>
              Retry
            </button>
          </div>
        )}

        {/* HTTP Stream from web_video_server */}
        {isStreaming && !error && (
          <img
            src={`http://localhost:8080/stream?topic=${imageTopic}&type=mjpeg`}
            alt="Camera Feed"
            className="camera-stream"
            onError={() => {
              // Fallback to canvas if HTTP stream fails
              setError('HTTP stream failed, trying ROS topic...');
              setTimeout(tryDirectImageSubscription, 100);
            }}
            onLoad={() => {
              setIsStreaming(true);
              setError(null);
            }}
          />
        )}

        {/* Canvas for ROS topic subscription */}
        <canvas
          ref={canvasRef}
          className="camera-canvas"
          style={{ display: (error || isStreaming) ? 'none' : 'block' }}
        />

        {!isStreaming && !error && isConnected && (
          <div className="loading-message">
            <p>📹 Connecting to camera...</p>
          </div>
        )}

        {!isConnected && (
          <div className="disconnected-message">
            <p>🔌 Connect to ROS to view camera feed</p>
          </div>
        )}
      </div>

      {imageData && (
        <div className="image-info">
          <span>Resolution: {imageData.width}x{imageData.height}</span>
          <span>Encoding: {imageData.encoding}</span>
          <span>Topic: {imageTopic}</span>
        </div>
      )}
    </div>
  );
};

export default CameraFeed;
