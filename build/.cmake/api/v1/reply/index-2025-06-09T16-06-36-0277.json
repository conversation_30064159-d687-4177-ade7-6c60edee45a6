{"cmake": {"generator": {"name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.16"}, "version": {"isDirty": false, "major": 3, "minor": 16, "patch": 3, "string": "3.16.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-4e0c2b55fead2edf45b8.json", "kind": "codemodel", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cache-v2-367f921d288a95b08ae2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-b898d329b8906957adbe.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-367f921d288a95b08ae2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-4e0c2b55fead2edf45b8.json", "kind": "codemodel", "version": {"major": 2, "minor": 0}}, {"error": "unknown request kind 'toolchains'"}, {"jsonFile": "cmakeFiles-v1-b898d329b8906957adbe.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}