<?xml version="1.0" encoding="UTF-8"?>
<Site BuildName="(empty)"
	BuildStamp="20250609-1606-Experimental"
	Name="abdulaziz-Latitude-E5550"
	Generator="ctest-3.16.3"
	CompilerName="/usr/bin/c++"
	CompilerVersion="9.4.0"
	OSName="Linux"
	Hostname="abdulaziz-Latitude-E5550"
	OSRelease="5.15.0-139-generic"
	OSVersion="#149~20.04.1-Ubuntu SMP Wed Apr 16 08:29:56 UTC 2025"
	OSPlatform="x86_64"
	Is64Bits="1"
	VendorString="GenuineIntel"
	VendorID="Intel Corporation"
	FamilyID="6"
	ModelID="61"
	ProcessorCacheSize="3072"
	NumberOfLogicalCPU="4"
	NumberOfPhysicalCPU="2"
	TotalVirtualMemory="9764"
	TotalPhysicalMemory="11864"
	LogicalProcessorsPerPhysical="2"
	ProcessorClockFrequency="2494.18"
	>
	<Testing>
		<StartDateTime>Jun 09 19:06 EEST</StartDateTime>
		<StartTestTime>1749485196</StartTestTime>
		<TestList/>
		<EndDateTime>Jun 09 19:06 EEST</EndDateTime>
		<EndTestTime>1749485196</EndTestTime>
		<ElapsedMinutes>0</ElapsedMinutes>
	</Testing>
</Site>
