# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/weednix_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/weednix_ws/src/build

# Include any dependencies generated for this target.
include visual-crop-row/CMakeFiles/visual_crop_row_core.dir/depend.make

# Include the progress variables for this target.
include visual-crop-row/CMakeFiles/visual_crop_row_core.dir/progress.make

# Include the compile flags for this target's objects.
include visual-crop-row/CMakeFiles/visual_crop_row_core.dir/flags.make

visual-crop-row/CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.o: visual-crop-row/CMakeFiles/visual_crop_row_core.dir/flags.make
visual-crop-row/CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.o: ../visual-crop-row/src/agribot_vs.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/weednix_ws/src/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object visual-crop-row/CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.o"
	cd /home/<USER>/weednix_ws/src/build/visual-crop-row && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.o -c /home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs.cpp

visual-crop-row/CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.i"
	cd /home/<USER>/weednix_ws/src/build/visual-crop-row && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs.cpp > CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.i

visual-crop-row/CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.s"
	cd /home/<USER>/weednix_ws/src/build/visual-crop-row && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs.cpp -o CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.s

# Object files for target visual_crop_row_core
visual_crop_row_core_OBJECTS = \
"CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.o"

# External object files for target visual_crop_row_core
visual_crop_row_core_EXTERNAL_OBJECTS =

devel/lib/libvisual_crop_row_core.so: visual-crop-row/CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.o
devel/lib/libvisual_crop_row_core.so: visual-crop-row/CMakeFiles/visual_crop_row_core.dir/build.make
devel/lib/libvisual_crop_row_core.so: visual-crop-row/CMakeFiles/visual_crop_row_core.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/weednix_ws/src/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library ../devel/lib/libvisual_crop_row_core.so"
	cd /home/<USER>/weednix_ws/src/build/visual-crop-row && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/visual_crop_row_core.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
visual-crop-row/CMakeFiles/visual_crop_row_core.dir/build: devel/lib/libvisual_crop_row_core.so

.PHONY : visual-crop-row/CMakeFiles/visual_crop_row_core.dir/build

visual-crop-row/CMakeFiles/visual_crop_row_core.dir/clean:
	cd /home/<USER>/weednix_ws/src/build/visual-crop-row && $(CMAKE_COMMAND) -P CMakeFiles/visual_crop_row_core.dir/cmake_clean.cmake
.PHONY : visual-crop-row/CMakeFiles/visual_crop_row_core.dir/clean

visual-crop-row/CMakeFiles/visual_crop_row_core.dir/depend:
	cd /home/<USER>/weednix_ws/src/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/weednix_ws/src /home/<USER>/weednix_ws/src/visual-crop-row /home/<USER>/weednix_ws/src/build /home/<USER>/weednix_ws/src/build/visual-crop-row /home/<USER>/weednix_ws/src/build/visual-crop-row/CMakeFiles/visual_crop_row_core.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : visual-crop-row/CMakeFiles/visual_crop_row_core.dir/depend

