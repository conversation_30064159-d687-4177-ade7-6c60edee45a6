# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile CXX with /usr/bin/c++
CXX_FLAGS = -Wall -Wextra -fPIC -g -fPIC   -std=gnu++11

CXX_DEFINES = -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"visual_crop_row\" -Dvisual_crop_row_core_EXPORTS

CXX_INCLUDES = -I/home/<USER>/weednix_ws/src/build/devel/include -I/home/<USER>/weednix_ws/src/visual-crop-row/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/usr/include/opencv4 -I/usr/include/eigen3 

